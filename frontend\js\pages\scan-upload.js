/**
 * 掃描上傳頁面 - 原生JavaScript版本
 */

class ScanUploadPage extends Component {
  constructor(props = {}) {
    super(props);
    this.state = {
      loading: false,
      frontImage: { file: null, preview: null, ocrText: '', parseStatus: null },
      backImage: { file: null, preview: null, ocrText: '', parseStatus: null },
      currentCaptureTarget: 'front',
      cameraModalVisible: false,
      parseLoading: false,
      cardData: {
        name: '',
        company_name: '',
        position: '',
        mobile_phone: '',
        office_phone: '',
        email: '',
        line_id: '',
        notes: '',
        company_address_1: '',
        company_address_2: ''
      }
    };
    
    this.cameraManager = null;
    this.mobileCameraModal = null;
    this.isMobileCameraMode = false;
  }

  render() {
    return `
      <div class="scan-upload-page">
        <!-- 導航欄 -->
        <div class="navbar">
          <button class="navbar-back" onclick="this.goBack()">
            <i class="fas fa-arrow-left"></i>
          </button>
          <div class="navbar-title">名片資料輸入</div>
          <div class="navbar-right"></div>
        </div>

        ${this.state.loading ? '<div class="loading-container"><div class="loading-spinner"></div><div class="loading-text">處理中...</div></div>' : ''}

        <div class="content">
          <!-- 圖片拍攝區域 -->
          <div class="card" style="margin-bottom: 16px;">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-camera"></i> 拍攝名片
              </h3>
            </div>
            <div class="card-body">
              <div class="image-capture-section">
                <!-- 拍攝模式切換與解析操作 -->
                <div class="capture-mode-switch" style="margin-bottom: 16px;">
                  <div class="space" style="width: 100%; justify-content: center; gap: 8px;">
                    <button
                      class="btn ${this.state.currentCaptureTarget === 'front' ? 'btn-primary' : 'btn-default'}"
                      onclick="this.switchCaptureTarget('front')"
                      style="flex: 1;"
                    >
                      正面
                    </button>
                    <button
                      class="btn ${this.state.currentCaptureTarget === 'back' ? 'btn-primary' : 'btn-default'}"
                      onclick="this.switchCaptureTarget('back')"
                      style="flex: 1;"
                    >
                      反面
                    </button>
                    <button
                      class="btn btn-warning"
                      onclick="this.handleManualParse()"
                      ${this.getCurrentImage().file ? '' : 'disabled'}
                      ${this.state.parseLoading ? 'disabled' : ''}
                      style="flex: 1;"
                    >
                      ${this.state.parseLoading ? '<i class="fas fa-spinner fa-spin"></i>' : '<i class="fas fa-search"></i>'} 解析
                    </button>
                  </div>
                </div>

                <!-- 單一拍攝框 -->
                <div class="single-capture-frame">
                  <div class="current-side-title" style="margin-bottom: 8px; font-size: 14px; font-weight: bold; text-align: center;">
                    當前拍攝: ${this.state.currentCaptureTarget === 'front' ? '正面' : '反面'}
                    ${this.renderParseStatusIcon(this.getCurrentImage().parseStatus)}
                  </div>

                  <!-- 顯示當前選中面的圖片 -->
                  ${this.renderCurrentImage()}

                  <!-- 拍攝操作按鈕 -->
                  <div style="display: flex; gap: 8px; justify-content: center; margin-top: 16px;">
                    <button
                      class="btn btn-primary"
                      onclick="this.startCamera()"
                      style="flex: 1;"
                    >
                      <i class="fas fa-camera"></i> 拍照
                    </button>
                    <button
                      class="btn btn-default"
                      onclick="this.selectFile()"
                      style="flex: 1;"
                    >
                      <i class="fas fa-images"></i> 相冊
                    </button>
                  </div>

                  <!-- 拍攝狀態指示 -->
                  <div class="capture-status" style="margin-top: 12px; text-align: center;">
                    <div class="space" style="justify-content: center; gap: 16px;">
                      <div class="status-indicator">
                        <div class="status-dot ${this.state.frontImage.preview ? 'active' : ''}"></div>
                        <span class="status-text ${this.state.frontImage.preview ? 'active' : ''}">
                          正面 ${this.state.frontImage.preview ? '已拍攝' : '未拍攝'}
                        </span>
                        ${this.renderParseStatusIcon(this.state.frontImage.parseStatus)}
                      </div>
                      <div class="status-indicator">
                        <div class="status-dot ${this.state.backImage.preview ? 'active' : ''}"></div>
                        <span class="status-text ${this.state.backImage.preview ? 'active' : ''}">
                          反面 ${this.state.backImage.preview ? '已拍攝' : '未拍攝'}
                        </span>
                        ${this.renderParseStatusIcon(this.state.backImage.parseStatus)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 隱藏的文件輸入 -->
              <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="this.handleFileSelect(event)">
            </div>
          </div>

          <!-- 統一的名片資料編輯表單 -->
          ${this.renderCardDataForm()}

          <!-- 操作按鈕 -->
          <div class="space space-vertical" style="width: 100%;">
            <button
              class="btn btn-success btn-large btn-block"
              onclick="this.handleSave()"
              ${this.state.loading ? 'disabled' : ''}
            >
              <i class="fas fa-check"></i> 保存名片
            </button>
          </div>
        </div>

        <!-- 相機模態框容器 -->
        <div id="camera-modal-placeholder"></div>
      </div>
    `;
  }

  renderCurrentImage() {
    const currentImage = this.getCurrentImage();

    if (currentImage.preview) {
      return `
        <div style="position: relative;">
          <img
            src="${currentImage.preview}"
            alt="名片${this.state.currentCaptureTarget === 'front' ? '正面' : '反面'}"
            style="
              width: 100%;
              height: clamp(280px, 40vw, 400px);
              object-fit: cover;
              border-radius: 8px;
              margin-bottom: 16px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              transition: all 0.3s ease;
            "
          >
          <button
            class="btn btn-danger"
            onclick="this.clearCurrentImage()"
            style="position: absolute; top: 8px; right: 8px; min-height: 32px; padding: 4px 8px;"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      `;
    } else {
      return `
        <div
          onclick="this.selectFile()"
          style="
            width: 100%;
            height: clamp(280px, 40vw, 400px);
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            margin-bottom: 16px;
            background: #fafafa;
            transition: all 0.3s ease;
            cursor: pointer;
          "
        >
          <i class="fas fa-camera" style="font-size: 64px; margin-bottom: 12px; color: #ccc;"></i>
          <div style="font-size: 16px; text-align: center;">
            請拍攝名片${this.state.currentCaptureTarget === 'front' ? '正面' : '反面'}
          </div>
          <div style="font-size: 14px; color: #bbb; margin-top: 8px;">
            點擊下方按鈕開始拍照
          </div>
        </div>
      `;
    }
  }

  // 獲取當前圖片對象
  getCurrentImage() {
    return this.state.currentCaptureTarget === 'front' ? this.state.frontImage : this.state.backImage;
  }

  // 渲染解析狀態圖標
  renderParseStatusIcon(status) {
    switch (status) {
      case 'parsing':
        return '<i class="fas fa-spinner fa-spin parse-status-icon parsing"></i>';
      case 'success':
        return '<i class="fas fa-check parse-status-icon success"></i>';
      case 'error':
        return '<i class="fas fa-times parse-status-icon error"></i>';
      default:
        return '';
    }
  }

  renderCardDataForm() {
    return `
      <div class="card" style="margin-bottom: 16px;">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-user-edit"></i> 名片資料
          </h3>
          <div class="card-extra">
            <i class="fas fa-edit"></i>
          </div>
        </div>
        <div class="card-body">
          <div class="form">
            <!-- 基本資訊 -->
            <div class="divider-text">基本資訊</div>

            <div class="form-item">
              <label class="form-label required">姓名</label>
              <input
                type="text"
                class="form-input"
                placeholder="請輸入姓名"
                value="${this.state.cardData.name}"
                onchange="this.updateCardData('name', this.value)"
              >
            </div>

            <div class="form-item">
              <label class="form-label">公司名稱</label>
              <input
                type="text"
                class="form-input"
                placeholder="請輸入公司名稱"
                value="${this.state.cardData.company_name}"
                onchange="this.updateCardData('company_name', this.value)"
              >
            </div>

            <div class="form-item">
              <label class="form-label">職位</label>
              <input
                type="text"
                class="form-input"
                placeholder="請輸入職位"
                value="${this.state.cardData.position}"
                onchange="this.updateCardData('position', this.value)"
              >
            </div>

            <!-- 聯絡資訊 -->
            <div class="divider-text">聯絡資訊</div>

            <div class="form-item">
              <label class="form-label">手機</label>
              <input
                type="tel"
                class="form-input"
                placeholder="請輸入手機號碼"
                value="${this.state.cardData.mobile_phone}"
                onchange="this.updateCardData('mobile_phone', this.value)"
              >
            </div>

            <div class="form-item">
              <label class="form-label">公司電話</label>
              <input
                type="tel"
                class="form-input"
                placeholder="請輸入公司電話"
                value="${this.state.cardData.office_phone}"
                onchange="this.updateCardData('office_phone', this.value)"
              >
            </div>

            <div class="form-item">
              <label class="form-label">Email</label>
              <input
                type="email"
                class="form-input"
                placeholder="請輸入Email地址"
                value="${this.state.cardData.email}"
                onchange="this.updateCardData('email', this.value)"
              >
            </div>

            <div class="form-item">
              <label class="form-label">Line ID</label>
              <input
                type="text"
                class="form-input"
                placeholder="請輸入Line ID"
                value="${this.state.cardData.line_id}"
                onchange="this.updateCardData('line_id', this.value)"
              >
            </div>

            <!-- 地址資訊 -->
            <div class="divider-text">地址資訊</div>

            <div class="form-item">
              <label class="form-label">公司地址一</label>
              <input
                type="text"
                class="form-input"
                placeholder="請輸入公司地址"
                value="${this.state.cardData.company_address_1}"
                onchange="this.updateCardData('company_address_1', this.value)"
              >
            </div>

            <div class="form-item">
              <label class="form-label">公司地址二</label>
              <input
                type="text"
                class="form-input"
                placeholder="請輸入公司地址（補充）"
                value="${this.state.cardData.company_address_2}"
                onchange="this.updateCardData('company_address_2', this.value)"
              >
            </div>

            <!-- 備註 -->
            <div class="divider-text">其他資訊</div>

            <div class="form-item">
              <label class="form-label">備註</label>
              <textarea
                class="form-textarea"
                placeholder="請輸入備註資訊"
                rows="3"
                onchange="this.updateCardData('notes', this.value)"
              >${this.state.cardData.notes}</textarea>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  async mount() {
    // 初始化相機管理器
    await this.initCameraManager();
    
    // 綁定事件
    this.bindEvents();
    
    // 檢測移動端相機模式
    this.detectCameraMode();
  }

  async initCameraManager() {
    try {
      this.cameraManager = getCameraManager();
      if (!this.cameraManager.isInitialized) {
        await this.cameraManager.initialize();
      }
    } catch (error) {
      console.error('相機管理器初始化失敗:', error);
    }
  }

  bindEvents() {
    // 綁定所有onclick事件
    const buttons = document.querySelectorAll('button[onclick], div[onclick]');
    buttons.forEach(element => {
      const onclickStr = element.getAttribute('onclick');
      if (onclickStr) {
        element.onclick = (event) => {
          event.preventDefault();
          this.executeOnclickFunction(onclickStr, event);
        };
      }
    });

    // 綁定輸入事件
    const inputs = document.querySelectorAll('input[onchange], textarea[onchange]');
    inputs.forEach(element => {
      const onchangeStr = element.getAttribute('onchange');
      if (onchangeStr) {
        element.onchange = (event) => {
          this.executeOnchangeFunction(onchangeStr, event);
        };
      }
    });
  }

  executeOnclickFunction(funcStr, event) {
    // 解析並執行onclick函數
    try {
      const func = new Function('event', `return (${funcStr}).call(this, event)`);
      func.call(this, event);
    } catch (error) {
      console.error('執行onclick函數失敗:', error);
    }
  }

  executeOnchangeFunction(funcStr, event) {
    // 解析並執行onchange函數
    try {
      const func = new Function('event', `
        const target = event.target;
        const value = target.value;
        return (${funcStr}).call(target, event);
      `);
      func.call(this, event);
    } catch (error) {
      console.error('執行onchange函數失敗:', error);
    }
  }

  detectCameraMode() {
    this.isMobileCameraMode = isMobileDevice();
  }

  goBack() {
    router.goBack();
  }

  switchCaptureTarget(target) {
    this.setState({ currentCaptureTarget: target });
  }

  selectFile() {
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
      fileInput.click();
    }
  }

  handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
      this.handleImageUpload(file);
    }
  }

  async handleImageUpload(file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const target = this.state.currentCaptureTarget;
      const newState = { ...this.state };
      
      if (target === 'front') {
        newState.frontImage = {
          file,
          preview: e.target.result,
          ocrText: '',
          parseStatus: null
        };
      } else {
        newState.backImage = {
          file,
          preview: e.target.result,
          ocrText: '',
          parseStatus: null
        };
      }
      
      this.setState(newState);
    };
    reader.readAsDataURL(file);

    // 自動執行OCR
    await this.performOCR(file);
  }

  clearCurrentImage() {
    const target = this.state.currentCaptureTarget;
    const newState = { ...this.state };

    if (target === 'front') {
      newState.frontImage = { file: null, preview: null, ocrText: '', parseStatus: null };
    } else {
      newState.backImage = { file: null, preview: null, ocrText: '', parseStatus: null };
    }

    this.setState(newState);
  }

  // 手動解析當前圖片
  async handleManualParse() {
    const currentImage = this.getCurrentImage();

    if (!currentImage.file) {
      Toast.error('請先拍攝或選擇圖片');
      return;
    }

    if (currentImage.ocrText) {
      // 如果已有OCR文本，直接解析
      this.setState({ parseLoading: true });
      try {
        await this.parseOCRData(currentImage.ocrText);
      } finally {
        this.setState({ parseLoading: false });
      }
    } else {
      // 如果沒有OCR文本，執行完整的OCR流程
      await this.performOCR(currentImage.file);
    }
  }

  // 更新圖片解析狀態
  updateImageParseStatus(side, status) {
    const newState = { ...this.state };
    if (side === 'front') {
      newState.frontImage.parseStatus = status;
    } else {
      newState.backImage.parseStatus = status;
    }
    this.setState(newState);
  }

  async startCamera() {
    if (!this.cameraManager) {
      Toast.error('相機管理器未初始化');
      return;
    }

    try {
      if (this.isMobileCameraMode) {
        // 移動端：使用全屏相機
        this.showMobileCamera();
      } else {
        // Web端：使用Modal相機
        this.showWebCamera();
      }
    } catch (error) {
      console.error('啟動相機失敗:', error);
      Toast.error('相機啟動失敗，請檢查權限');
    }
  }

  showMobileCamera() {
    if (!this.mobileCameraModal) {
      this.mobileCameraModal = new MobileCameraModal({
        visible: false,
        onClose: () => this.hideMobileCamera(),
        onPhotoTaken: (photoData) => this.handleMobilePhotoTaken(photoData),
        cameraManager: this.cameraManager,
        target: this.state.currentCaptureTarget === 'front' ? 'front' : 'back'
      });
    }
    
    this.mobileCameraModal.updateOptions({
      visible: true,
      target: this.state.currentCaptureTarget === 'front' ? 'front' : 'back'
    });
  }

  hideMobileCamera() {
    if (this.mobileCameraModal) {
      this.mobileCameraModal.updateOptions({ visible: false });
    }
  }

  async handleMobilePhotoTaken(photoData) {
    const file = uploadUtils.blobToFile(photoData.blob, `${this.state.currentCaptureTarget}_card.jpg`);
    await this.handleImageUpload(file);
  }

  showWebCamera() {
    // Web端相機實現
    const modal = Modal.show({
      title: '拍攝名片',
      content: `
        <div class="camera-modal">
          <video id="cameraVideo" autoplay playsinline style="width: 100%; height: 350px; object-fit: cover; border-radius: 8px; background: #000;"></video>
          <canvas id="cameraCanvas" style="display: none;"></canvas>
          <div style="margin-top: 16px; text-align: center;">
            <button class="btn btn-primary btn-large" onclick="this.capturePhoto()">
              <i class="fas fa-camera"></i> 拍照
            </button>
          </div>
        </div>
      `
    });

    // 啟動Web相機
    setTimeout(async () => {
      const video = document.getElementById('cameraVideo');
      const canvas = document.getElementById('cameraCanvas');
      
      if (video && canvas) {
        try {
          this.cameraManager.setElements(video, canvas);
          await this.cameraManager.startCamera(this.state.currentCaptureTarget === 'front' ? 'front' : 'back');
          
          // 綁定拍照按鈕
          window.capturePhoto = async () => {
            try {
              const photoData = await this.cameraManager.takePhoto();
              const file = uploadUtils.blobToFile(photoData.blob, `${this.state.currentCaptureTarget}_card.jpg`);
              await this.handleImageUpload(file);
              modal.hide();
            } catch (error) {
              console.error('拍照失敗:', error);
              Toast.error('拍照失敗，請重試');
            }
          };
        } catch (error) {
          console.error('Web相機啟動失敗:', error);
          Toast.error('相機啟動失敗');
          modal.hide();
        }
      }
    }, 100);
  }

  async performOCR(file) {
    const targetFile = file || this.getCurrentImage().file;
    const side = this.state.currentCaptureTarget;

    if (!targetFile) {
      Toast.error('請先選擇或拍攝圖片');
      return;
    }

    this.setState({ loading: true });
    this.updateImageParseStatus(side, 'parsing');

    try {
      // 執行OCR識別
      const ocrResult = await ocrApi.recognize(targetFile);

      if (ocrResult.text) {
        // 更新OCR文本
        const newState = { ...this.state };
        if (side === 'front') {
          newState.frontImage.ocrText = ocrResult.text;
        } else {
          newState.backImage.ocrText = ocrResult.text;
        }
        this.setState(newState);

        // 執行智能解析並填充表單
        await this.parseAndFillOCRData(ocrResult.text, side);

        Toast.success(`${side === 'front' ? '正面' : '反面'}OCR識別完成！`);
      } else {
        this.updateImageParseStatus(side, 'error');
        Toast.warning('未識別到文字內容');
      }
    } catch (error) {
      console.error('OCR識別失敗:', error);
      this.updateImageParseStatus(side, 'error');
      Toast.error('OCR識別失敗，請檢查網絡連接並重試');
    } finally {
      this.setState({ loading: false });
    }
  }

  // 智能解析OCR文字並填充表單
  async parseAndFillOCRData(ocrText, side) {
    if (!ocrText) return;

    try {
      this.updateImageParseStatus(side, 'parsing');

      // 調用後端智能解析API
      const parseResult = await ocrApi.parseFields(ocrText, side);

      if (parseResult.data) {
        const parsedFields = parseResult.data;

        // 更新表單數據，保留已有數據，只更新新解析到的欄位
        const newCardData = { ...this.state.cardData };

        // 智能合併邏輯：如果欄位已有數據，則保留；如果沒有，則使用新解析的數據
        Object.keys(parsedFields).forEach(field => {
          if (parsedFields[field] && (!newCardData[field] || newCardData[field].trim() === '')) {
            newCardData[field] = parsedFields[field];
          }
        });

        this.setState({ cardData: newCardData });
        this.updateImageParseStatus(side, 'success');

        Toast.success(`${side === 'front' ? '正面' : '反面'}資料解析完成！已自動填入相關欄位`);
      } else {
        this.updateImageParseStatus(side, 'error');
        Toast.warning('OCR資料解析失敗，請手動編輯');
      }
    } catch (error) {
      console.error('OCR解析錯誤:', error);
      this.updateImageParseStatus(side, 'error');
      Toast.error('OCR資料解析失敗，請檢查網絡連接');
    }
  }

  async parseOCRData(ocrText) {
    // 保持向後兼容
    await this.parseAndFillOCRData(ocrText, this.state.currentCaptureTarget);
  }

  updateCardData(field, value) {
    const newCardData = { ...this.state.cardData };
    newCardData[field] = value;
    this.setState({ cardData: newCardData });
  }

  async handleSave() {
    // 驗證必填欄位
    if (!this.state.cardData.name.trim()) {
      Toast.error('請輸入姓名');
      return;
    }

    this.setState({ loading: true });

    try {
      const saveData = { ...this.state.cardData };
      
      // 添加圖片文件
      if (this.state.frontImage.file) {
        saveData.front_image = this.state.frontImage.file;
      }
      if (this.state.backImage.file) {
        saveData.back_image = this.state.backImage.file;
      }

      const response = await cardsApi.create(saveData);

      if (response) {
        Toast.success('名片保存成功！');
        router.navigate('/cards');
      }
    } catch (error) {
      console.error('保存失敗:', error);
      errorHandler.showError(error);
    } finally {
      this.setState({ loading: false });
    }
  }

  unmount() {
    // 停止相機
    if (this.cameraManager) {
      this.cameraManager.stopCamera();
    }
    
    // 銷毀移動端相機模態框
    if (this.mobileCameraModal) {
      this.mobileCameraModal.destroy();
      this.mobileCameraModal = null;
    }
  }
}

// 導出到全局
window.ScanUploadPage = ScanUploadPage;

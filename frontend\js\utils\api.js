/**
 * API調用工具 - 原生JavaScript版本
 * 提供統一的API調用接口
 */

// API基礎配置
const API_BASE_URL = '/api/v1';

/**
 * HTTP請求工具類
 */
class ApiClient {
  constructor(baseURL = API_BASE_URL) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json'
    };
    this.retryAttempts = 3;
    this.retryDelay = 1000;
  }

  /**
   * 請求重試機制
   */
  async requestWithRetry(url, options = {}, attempt = 1) {
    try {
      return await this.request(url, options);
    } catch (error) {
      // 如果是客戶端錯誤（4xx）或已達到最大重試次數，直接拋出錯誤
      if (error.status >= 400 && error.status < 500 || attempt >= this.retryAttempts) {
        throw error;
      }

      // 網絡錯誤或服務器錯誤（5xx）進行重試
      if (error.name === 'NetworkError' || error.name === 'TimeoutError' ||
          (error.status >= 500 && error.status < 600)) {

        console.warn(`請求失敗，正在進行第 ${attempt} 次重試...`);

        // 等待一段時間後重試
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));

        return this.requestWithRetry(url, options, attempt + 1);
      }

      throw error;
    }
  }

  /**
   * 發送HTTP請求
   */
  async request(url, options = {}) {
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;

    const config = {
      headers: {
        ...this.defaultHeaders,
        ...options.headers
      },
      ...options
    };

    // 添加請求超時
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout || 30000);
    config.signal = controller.signal;

    try {
      // 顯示加載狀態
      if (options.showLoading !== false && window.Loading) {
        Loading.show('請求中...');
      }

      const response = await fetch(fullUrl, config);
      clearTimeout(timeoutId);

      // 檢查響應狀態
      if (!response.ok) {
        let errorData = {};
        try {
          errorData = await response.json();
        } catch (e) {
          errorData = { detail: response.statusText };
        }

        const error = new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
        error.status = response.status;
        error.data = errorData;
        throw error;
      }

      // 處理不同的響應類型
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        return data;
      } else {
        return await response.text();
      }
    } catch (error) {
      clearTimeout(timeoutId);

      // 處理不同類型的錯誤
      if (error.name === 'AbortError') {
        const timeoutError = new Error('請求超時，請檢查網絡連接');
        timeoutError.name = 'TimeoutError';
        throw timeoutError;
      }

      if (!navigator.onLine) {
        const networkError = new Error('網絡連接不可用，請檢查網絡設置');
        networkError.name = 'NetworkError';
        throw networkError;
      }

      console.error('API請求失敗:', error);
      throw error;
    } finally {
      // 隱藏加載狀態
      if (options.showLoading !== false && window.Loading) {
        Loading.hide();
      }
    }
  }

  /**
   * GET請求
   */
  async get(url, params = {}) {
    const searchParams = new URLSearchParams(params);
    const queryString = searchParams.toString();
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    return this.request(fullUrl, {
      method: 'GET'
    });
  }

  /**
   * POST請求
   */
  async post(url, data = {}, options = {}) {
    const config = {
      method: 'POST',
      ...options
    };

    // 處理不同類型的數據
    if (data instanceof FormData) {
      // FormData不需要設置Content-Type，瀏覽器會自動設置
      delete config.headers;
      config.body = data;
    } else if (typeof data === 'object') {
      config.body = JSON.stringify(data);
    } else {
      config.body = data;
    }

    return this.request(url, config);
  }

  /**
   * PUT請求
   */
  async put(url, data = {}, options = {}) {
    return this.post(url, data, { ...options, method: 'PUT' });
  }

  /**
   * DELETE請求
   */
  async delete(url, options = {}) {
    return this.request(url, {
      method: 'DELETE',
      ...options
    });
  }
}

// 創建API客戶端實例
const apiClient = new ApiClient();

/**
 * 名片相關API
 */
const cardsApi = {
  /**
   * 獲取所有名片
   */
  async getAll(params = {}) {
    try {
      return await apiClient.requestWithRetry('/cards/', {
        method: 'GET',
        showLoading: false
      });
    } catch (error) {
      console.error('獲取名片列表失敗:', error);
      throw error;
    }
  },

  /**
   * 根據ID獲取名片
   */
  async getById(id) {
    if (!id) {
      throw new Error('名片ID不能為空');
    }

    try {
      return await apiClient.requestWithRetry(`/cards/${id}`, {
        method: 'GET'
      });
    } catch (error) {
      console.error(`獲取名片 ${id} 失敗:`, error);
      throw error;
    }
  },

  /**
   * 創建新名片
   */
  async create(cardData) {
    if (!cardData || !cardData.name) {
      throw new Error('名片姓名不能為空');
    }

    try {
      const formData = new FormData();

      // 添加名片數據
      Object.keys(cardData).forEach(key => {
        if (cardData[key] !== null && cardData[key] !== undefined && cardData[key] !== '') {
          formData.append(key, cardData[key]);
        }
      });

      return await apiClient.requestWithRetry('/cards/', {
        method: 'POST',
        body: formData,
        headers: {}, // 讓瀏覽器自動設置Content-Type
        timeout: 60000 // 文件上傳需要更長時間
      });
    } catch (error) {
      console.error('創建名片失敗:', error);
      throw error;
    }
  },

  /**
   * 更新名片
   */
  async update(id, cardData) {
    if (!id) {
      throw new Error('名片ID不能為空');
    }

    if (!cardData || !cardData.name) {
      throw new Error('名片姓名不能為空');
    }

    try {
      const formData = new FormData();

      // 添加名片數據
      Object.keys(cardData).forEach(key => {
        if (cardData[key] !== null && cardData[key] !== undefined && cardData[key] !== '') {
          formData.append(key, cardData[key]);
        }
      });

      return await apiClient.requestWithRetry(`/cards/${id}`, {
        method: 'PUT',
        body: formData,
        headers: {}, // 讓瀏覽器自動設置Content-Type
        timeout: 60000
      });
    } catch (error) {
      console.error(`更新名片 ${id} 失敗:`, error);
      throw error;
    }
  },

  /**
   * 刪除名片
   */
  async delete(id) {
    if (!id) {
      throw new Error('名片ID不能為空');
    }

    try {
      return await apiClient.requestWithRetry(`/cards/${id}`, {
        method: 'DELETE'
      });
    } catch (error) {
      console.error(`刪除名片 ${id} 失敗:`, error);
      throw error;
    }
  },

  /**
   * 搜索名片
   */
  async search(query) {
    if (!query || !query.trim()) {
      return [];
    }

    try {
      return await apiClient.requestWithRetry('/cards/search', {
        method: 'GET',
        showLoading: false
      });
    } catch (error) {
      console.error('搜索名片失敗:', error);
      throw error;
    }
  }
};

/**
 * OCR相關API
 */
const ocrApi = {
  /**
   * 執行OCR識別
   */
  async recognize(imageFile, options = {}) {
    if (!imageFile) {
      throw new Error('圖片文件不能為空');
    }

    // 檢查文件類型
    if (!imageFile.type.startsWith('image/')) {
      throw new Error('請選擇有效的圖片文件');
    }

    // 檢查文件大小（限制10MB）
    if (imageFile.size > 10 * 1024 * 1024) {
      throw new Error('圖片文件大小不能超過10MB');
    }

    try {
      const formData = new FormData();
      formData.append('file', imageFile);

      // 添加其他選項
      Object.keys(options).forEach(key => {
        if (options[key] !== null && options[key] !== undefined) {
          formData.append(key, options[key]);
        }
      });

      return await apiClient.requestWithRetry('/ocr/recognize', {
        method: 'POST',
        body: formData,
        headers: {}, // 讓瀏覽器自動設置Content-Type
        timeout: 120000 // OCR處理需要更長時間
      });
    } catch (error) {
      console.error('OCR識別失敗:', error);
      throw error;
    }
  },

  /**
   * 解析OCR文本為結構化數據
   */
  async parse(ocrText, options = {}) {
    if (!ocrText || !ocrText.trim()) {
      throw new Error('OCR文本不能為空');
    }

    try {
      return await apiClient.requestWithRetry('/ocr/parse', {
        method: 'POST',
        body: JSON.stringify({
          text: ocrText.trim(),
          ...options
        }),
        timeout: 30000
      });
    } catch (error) {
      console.error('OCR文本解析失敗:', error);
      throw error;
    }
  },

  /**
   * 智能解析OCR文字並填充表單字段 - 與React版本保持一致
   */
  async parseFields(ocrText, side = 'front') {
    if (!ocrText || !ocrText.trim()) {
      throw new Error('OCR文本不能為空');
    }

    try {
      return await apiClient.requestWithRetry('/ocr/parse-fields', {
        method: 'POST',
        body: JSON.stringify({
          ocr_text: ocrText.trim(),
          side: side
        }),
        timeout: 30000
      });
    } catch (error) {
      console.error('OCR字段解析失敗:', error);
      throw error;
    }
  },

  /**
   * 一步式OCR+解析
   */
  async recognizeAndParse(imageFile, options = {}) {
    if (!imageFile) {
      throw new Error('圖片文件不能為空');
    }

    // 檢查文件類型和大小
    if (!imageFile.type.startsWith('image/')) {
      throw new Error('請選擇有效的圖片文件');
    }

    if (imageFile.size > 10 * 1024 * 1024) {
      throw new Error('圖片文件大小不能超過10MB');
    }

    try {
      const formData = new FormData();
      formData.append('file', imageFile);

      Object.keys(options).forEach(key => {
        if (options[key] !== null && options[key] !== undefined) {
          formData.append(key, options[key]);
        }
      });

      return await apiClient.requestWithRetry('/ocr/recognize-and-parse', {
        method: 'POST',
        body: formData,
        headers: {},
        timeout: 120000
      });
    } catch (error) {
      console.error('OCR識別和解析失敗:', error);
      throw error;
    }
  },

  /**
   * 執行圖片OCR識別 - 與React版本API路徑保持一致
   */
  async image(imageFile, options = {}) {
    if (!imageFile) {
      throw new Error('圖片文件不能為空');
    }

    // 檢查文件類型
    if (!imageFile.type.startsWith('image/')) {
      throw new Error('請選擇有效的圖片文件');
    }

    // 檢查文件大小（限制10MB）
    if (imageFile.size > 10 * 1024 * 1024) {
      throw new Error('圖片文件大小不能超過10MB');
    }

    try {
      const formData = new FormData();
      formData.append('file', imageFile);

      // 添加其他選項
      Object.keys(options).forEach(key => {
        if (options[key] !== null && options[key] !== undefined) {
          formData.append(key, options[key]);
        }
      });

      return await apiClient.requestWithRetry('/ocr/image', {
        method: 'POST',
        body: formData,
        headers: {}, // 讓瀏覽器自動設置Content-Type
        timeout: 120000 // OCR處理需要更長時間
      });
    } catch (error) {
      console.error('OCR圖片識別失敗:', error);
      throw error;
    }
  }
};

/**
 * 文件上傳工具
 */
const uploadUtils = {
  /**
   * 將Blob轉換為File對象
   */
  blobToFile(blob, filename = 'image.jpg') {
    return new File([blob], filename, { type: blob.type });
  },

  /**
   * 將DataURL轉換為Blob
   */
  dataURLToBlob(dataURL) {
    const arr = dataURL.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    
    return new Blob([u8arr], { type: mime });
  },

  /**
   * 將DataURL轉換為File
   */
  dataURLToFile(dataURL, filename = 'image.jpg') {
    const blob = this.dataURLToBlob(dataURL);
    return this.blobToFile(blob, filename);
  },

  /**
   * 壓縮圖片
   */
  async compressImage(file, maxWidth = 1920, maxHeight = 1080, quality = 0.9) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        // 計算新尺寸
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }
        
        // 設置畫布尺寸
        canvas.width = width;
        canvas.height = height;
        
        // 繪製圖片
        ctx.drawImage(img, 0, 0, width, height);
        
        // 轉換為Blob
        canvas.toBlob(resolve, 'image/jpeg', quality);
      };
      
      img.src = URL.createObjectURL(file);
    });
  }
};

/**
 * 錯誤處理工具
 */
const errorHandler = {
  /**
   * 處理API錯誤
   */
  handleApiError(error) {
    let message = '操作失敗，請重試';
    let type = 'error';

    // 處理網絡錯誤
    if (error.name === 'NetworkError') {
      message = '網絡連接不可用，請檢查網絡設置';
      type = 'warning';
    } else if (error.name === 'TimeoutError') {
      message = '請求超時，請檢查網絡連接或稍後重試';
      type = 'warning';
    } else if (error.status) {
      // 處理HTTP狀態碼錯誤
      switch (error.status) {
        case 400:
          message = error.data?.detail || '請求參數錯誤，請檢查輸入信息';
          break;
        case 401:
          message = '身份驗證失敗，請重新登錄';
          break;
        case 403:
          message = '權限不足，無法執行此操作';
          break;
        case 404:
          message = '請求的資源不存在';
          break;
        case 413:
          message = '上傳文件過大，請選擇較小的文件';
          break;
        case 422:
          message = error.data?.detail || '數據驗證失敗，請檢查輸入信息';
          break;
        case 429:
          message = '請求過於頻繁，請稍後重試';
          type = 'warning';
          break;
        case 500:
          message = '服務器內部錯誤，請稍後重試';
          break;
        case 502:
        case 503:
        case 504:
          message = '服務暫時不可用，請稍後重試';
          type = 'warning';
          break;
        default:
          message = error.message || `服務器錯誤 (${error.status})`;
      }
    } else if (error.message) {
      message = error.message;
    }

    return { message, type };
  },

  /**
   * 顯示錯誤提示
   */
  showError(error) {
    const { message, type } = this.handleApiError(error);

    if (window.Toast) {
      if (type === 'warning') {
        Toast.warning(message, 4000);
      } else {
        Toast.error(message, 5000);
      }
    } else {
      alert(message);
    }

    // 記錄錯誤到控制台
    console.error('API錯誤:', {
      message,
      type,
      originalError: error,
      timestamp: new Date().toISOString()
    });
  },

  /**
   * 顯示成功提示
   */
  showSuccess(message, duration = 3000) {
    if (window.Toast) {
      Toast.success(message, duration);
    } else {
      console.log('成功:', message);
    }
  },

  /**
   * 顯示警告提示
   */
  showWarning(message, duration = 4000) {
    if (window.Toast) {
      Toast.warning(message, duration);
    } else {
      console.warn('警告:', message);
    }
  },

  /**
   * 顯示信息提示
   */
  showInfo(message, duration = 3000) {
    if (window.Toast) {
      Toast.show({ content: message, duration });
    } else {
      console.info('信息:', message);
    }
  }
};

// 導出到全局
window.ApiClient = ApiClient;
window.apiClient = apiClient;
window.cardsApi = cardsApi;
window.ocrApi = ocrApi;
window.uploadUtils = uploadUtils;
window.errorHandler = errorHandler;

// 為了兼容性，也導出原有的axios風格API
window.axios = {
  get: (url, config) => apiClient.get(url, config?.params),
  post: (url, data, config) => apiClient.post(url, data, config),
  put: (url, data, config) => apiClient.put(url, data, config),
  delete: (url, config) => apiClient.delete(url, config)
};
